export default function useFollow() {
    const loading = ref(false);

    async function followUser(targetUid) {
        console.log('followUser', targetUid);
        if (!myWebview.isInApp()) {
            return false;
        }
        return new Promise((resolve) => {
            const CALLBACK_FUNC_NAME = `$activityFollowUserCallback${Date.now()}`;
            window[CALLBACK_FUNC_NAME] = (res) => {
                try {
                    const data = JSON.parse(res);
                    if (data?.isSuccess) {
                        resolve(true);
                    }
                    else {
                        resolve(false);
                    }
                }
                catch (error) {
                    resolve(false);
                }
                loading.value = false;
                window[CALLBACK_FUNC_NAME] = undefined;
            };
            try {
                loading.value = true;
                TTJSBridge.invoke('operate', 'followUser', `${targetUid}`, `window.${CALLBACK_FUNC_NAME}`);
            }
            catch (error) {
                window[CALLBACK_FUNC_NAME]({});
            }
        });
    }

    return {
        loading,
        followUser,
    };
}

// const { loading: following, followUser } = useFollow();
// followUser(uid)
