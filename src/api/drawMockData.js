// src/api/drawMockData.js
// 根据 test.proto 生成的 mock 数据

/**
 * 生成随机用户信息
 */
function generateUserInfo(uid = null) {
    const usernames = ['小明', '小红', '小刚', '小丽', '小华', '小美', '小强', '小芳', '小杰', '小雪'];
    const aliases = ['sunshine', 'moonlight', 'starlight', 'rainbow', 'butterfly', 'phoenix', 'dragon', 'tiger', 'eagle', 'wolf'];
    const nicknames = ['阳光少年', '月光女神', '星光璀璨', '彩虹之约', '蝴蝶飞舞', '凤凰涅槃', '龙腾四海', '虎啸山林', '雄鹰展翅', '狼行天下'];

    const randomUid = uid || Math.floor(Math.random() * 1000000) + 10000;
    const index = randomUid % usernames.length;

    return {
        uid: randomUid,
        username: usernames[index],
        alias: aliases[index],
        nickname: nicknames[index],
        sex: Math.random() > 0.5 ? 1 : 2, // 1为男，非1为女
        guildInfo: Math.random() > 0.3
            ? {
                    name: `公会${Math.floor(Math.random() * 100) + 1}`,
                    guildId: Math.floor(Math.random() * 10000) + 1000,
                    displayId: Math.floor(Math.random() * 999) + 100,
                }
            : null,
        role: Math.random() > 0.7 ? Math.floor(Math.random() * 5) + 1 : null,
    };
}

/**
 * 生成频道信息
 */
function generateChannelInfo() {
    const statuses = [1, 2, 3, 4]; // LEAVE, STAY, WATCH, LIVE, PK
    return {
        channelId: Math.floor(Math.random() * 100000) + 10000,
        status: statuses[Math.floor(Math.random() * statuses.length)],
    };
}

/**
 * 生成MVP信息
 */
function generateMvpInfo() {
    const value = Math.floor(Math.random() * 1000000) + 1000;
    return {
        rank: Math.floor(Math.random() * 10) + 1,
        value,
        valueHuman: `${(value / 1000).toFixed(1)}k`,
        userInfo: generateUserInfo(),
    };
}

/**
 * 生成通用排行榜项目
 */
function generateCommonRankItem(rank = null) {
    const value = Math.floor(Math.random() * 1000000) + 1000;
    const ltPrevValue = Math.floor(Math.random() * 10000) + 100;
    const gtNextValue = Math.floor(Math.random() * 10000) + 100;

    return {
        uid: Math.floor(Math.random() * 1000000) + 10000,
        rank: rank || Math.floor(Math.random() * 100) + 1,
        rankHuman: `第${rank || Math.floor(Math.random() * 100) + 1}名`,
        value,
        valueHuman: `${(value / 1000).toFixed(1)}k`,
        ltPrevValue,
        ltPrevValueHuman: `${ltPrevValue}`,
        gtNextValue,
        gtNextValueHuman: `${gtNextValue}`,
        userInfo: generateUserInfo(),
        channelInfo: generateChannelInfo(),
        mvpInfoList: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, () => generateMvpInfo()),
    };
}

const API = {
    // 心动挑战页面数据
    getHeartbeatChallengeData(params = {}) {
        return {
            user: generateUserInfo(params.uid || 12345),
            value: Math.floor(Math.random() * 30) + 1, // 参与天数
            anchorWork: {
                anchor: generateUserInfo(67890),
                workerUid: 67890,
                introduction: '这是一位才华横溢的主播，擅长唱歌跳舞，深受观众喜爱。',
                workLink: 'https://obs-cdn.52tt.com/tt/fe-moss/web/cazihanhao/20240105143536_80954081.mp3',
                channelId: Math.random() > 0.5 ? `${Math.floor(Math.random() * 100000) + 10000}` : null,
            },
            serverTime: Math.floor(Date.now() / 1000),
            startTime: Math.floor(Date.now() / 1000) - 86400 * 7, // 7天前开始
            endTime: Math.floor(Date.now() / 1000) + 86400 * 7, // 7天后结束
        };
    },

    // 心动指数榜单
    getRank(params = {}) {
        const { page = 1, size = 10 } = params;
        const startIndex = (page - 1) * size;
        return {
            total: 156,
            list: Array.from({ length: size }, (_, index) =>
                generateCommonRankItem(startIndex + index + 1)),
            self: generateCommonRankItem(Math.floor(Math.random() * 100) + 1),
        };
    },

    // 播报列表
    getBroadcastList() {
        return {
            list: Array.from({ length: 5 }, () => ({
                user: generateUserInfo(),
                str: `恭喜获得${['钻石', '金币', '礼物', '道具', '特权'][Math.floor(Math.random() * 5)]}奖励！`,
                rewardId: `reward_${Math.floor(Math.random() * 1000) + 1}`,
            })),
        };
    },

    // 设置分数
    setScore() {
        return {
            data: 'success',
        };
    },

    // 初始化数据
    init(params = {}) {
        return {
            serverTime: Math.floor(Date.now() / 1000),
            startTime: Math.floor(Date.now() / 1000) - 86400 * 7,
            endTime: Math.floor(Date.now() / 1000) + 86400 * 7,
            userInfo: generateUserInfo(params.uid || 12345),
        };
    },
};

const getDrawMockData = (type, payload) =>
    new Promise((resolve) => {
        const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
        let data;
        if (typeof API[type] === 'function')
            data = API[type](payload);
        else data = API[type] || {};

        // eslint-disable-next-line no-console
        console.log(
            `模拟抽奖接口请求名称<=== ${type} delay: ${delay} ms; payload: ${JSON.stringify(payload)}`,
        );
        window.setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('模拟抽奖接口请求返回===>', data);
            resolve({
                code: 0,
                data,
                msg: '',
            });
        }, delay);
    });

export default getDrawMockData;
