const API = {
    init() {
        return {
            serverTime: new Date('2025/08/25 12:00:00').getTime() / 1000,
            startTime: new Date('2025/08/18 00:00:00').getTime() / 1000,
            endTime: new Date('2025/08/24 23:59:59').getTime() / 1000,
            userInfo: {
                uid: 2416206,
                username: 'tt110200509',
                alias: 'Mock-alias',
                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                sex: 1,
            },
            anchorInfo: {
                userInfo: {
                    uid: 2416206,
                    username: 'tt110328341',
                    alias: 'Mock-alias',
                    nickname: '跟随主播',
                    sex: 1,
                },
                identity: 1,
                level: 2,
                // bindInfo: {},
                teamBuff: 0.1,
                dailyTeamRaceId: 'D',
            },
            identity: 1,
            level: 6,

        };
    },
    invite() {
        return {};
    },
    acceptInvite() {
        return {};
    },
    getInviteList() {
        return {
            list: [
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 1, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
            ],
            total: 20,
        };
    },
    getAcceptList() {
        return {
            list: [
                { userInfo: {}, level: 6, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 5, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 4, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 3, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 1, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
                { userInfo: {}, level: 2, identity: 2, acceptStatus: 1 },
            ],
            total: 30,
        };
    },
    getTeamStarlightRecord() {
        return {
            list: [
                { id: 1, value: 2, limit: 1, completedCount: 100, curValue: 2, doneCount: 3660 },
                { id: 1, value: 2, limit: 1, completedCount: 5, curValue: 2, doneCount: 400 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2, doneCount: 400 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2, doneCount: 400 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2, doneCount: 400 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2, doneCount: 401 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2, doneCount: 400 },
            ],
            totalValue: 2,
        };
    },
    getMyStarlight() {
        return { myStarlight: 2, rewardStatus: 2, rewardNum: 1 };
    },
    receiveDailyReward() {
        return {};
    },
    getDailyLuckyList() {
        return {
            list1: [
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
            ],
            list: [],
        };
    },
    getOfficialAnchorList() {
        return {
            list: [

                {
                    rank: '5',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    isFollow: true,
                    introductionText: '哈喽喽喽喽喽喽喽喽喽喽喽喽哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    startTime: new Date('2025/08/19 20:39:00').getTime() / 1000,
                    endTime: new Date('2025/08/19 21:00:59').getTime() / 1000,
                    channelInfo: {
                        channelId: 213123,
                        status: 4,
                    },
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '6',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    isFollow: true,
                    introductionText: '哈喽喽喽喽喽喽喽喽喽喽喽喽哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    startTime: new Date('2025/08/19 20:39:00').getTime() / 1000,
                    endTime: new Date('2025/08/19 21:00:59').getTime() / 1000,
                    channelInfo: {
                        channelId: 213123,
                        status: 2,
                    },
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '7',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    isFollow: true,
                    introductionText: '哈喽喽喽喽喽喽喽喽喽喽喽喽哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    startTime: new Date('2025/08/19 21:10:00').getTime() / 1000,
                    endTime: new Date('2025/08/19 21:20:59').getTime() / 1000,
                    channelInfo: {
                        channelId: 213123,
                        status: 4,
                    },
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '8',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '9',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    isFollow: false,
                    introductionText: '哈喽喽喽喽喽喽喽喽喽喽喽喽哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    startTime: new Date('2025/08/19 20:39:00').getTime() / 1000,
                    endTime: new Date('2025/08/19 21:00:59').getTime() / 1000,
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '10',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    isFollow: false,
                    introductionText: '哈喽喽喽喽喽喽喽喽喽喽喽喽哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    startTime: new Date('2025/08/19 20:39:00').getTime() / 1000,
                    endTime: new Date('2025/08/19 21:00:59').getTime() / 1000,
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
            ],
            rewardStatus: 1,
        };
    },
    receiveOfficialReward() {
        return {};
    },
    getStarlightRank() {
        return {
            total: 1,
            list: [
                {
                    rank: '1',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                        identity: 1,
                    },
                    channelInfo: {
                        channelId: 111,
                        status: 3,
                    },
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                            identity: 2,
                        },
                        channelInfo: {},
                        level: 6,
                    },
                    money: 1000,
                },
                {
                    rank: '2',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                        identity: 1,
                    },
                    channelInfo: {
                        channelId: 111,
                        status: 1,
                    },
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                            identity: 2,
                        },
                        channelInfo: {},
                        level: 5,
                    },
                    money: 1000,
                },
                {
                    rank: '3',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                        identity: 1,
                    },
                    channelInfo: {
                        channelId: 111,
                        status: 3,
                    },
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                            identity: 2,
                        },
                        channelInfo: {},
                        level: 4,
                    },
                },
                {
                    rank: '4',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '5',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '6',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '7',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '8',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '9',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '10',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
            ],
            self2: null,
            self: {
                userInfo: {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                money: 3000,
                rank: '2',
                value: 2,
                prevDescribe: 'Mock-prevDescribe',
                nextDescribe: 'Mock-nextDescribe',
                discipleInfo: {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    level: 2,
                },
            },
        };
    },
};
const getMockData = (type, payload) =>
    new Promise((resolve) => {
        const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
        let data;
        if (typeof API[type] === 'function')
            data = API[type](payload);
        else data = API[type];

        // eslint-disable-next-line no-console
        console.log(
            `模拟接口请求名称<=== ${type} delay: ${delay} ms; payload: ${JSON.stringify(payload)}`,
        );
        window.setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('模拟接口请求返回===>', data);
            resolve({
                code: 0,
                data,
                msg: '',
            });
        }, delay);
    });
export default getMockData;
