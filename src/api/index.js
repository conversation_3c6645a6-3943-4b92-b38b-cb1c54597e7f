// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({ proPrefix = '/activity.Activity/', api, data = {}, config = {} }) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */
const REQUEST_API_MAP = {
    init: 'init',
    invite: 'invite',
    acceptInvite: 'acceptInvite',
    getInviteList: 'getInviteList',
    getAcceptList: 'getAcceptList',
    getTeamStarlightRecord: 'getTeamStarlightRecord',
    getMyStarlight: 'getMyStarlight',
    receiveDailyReward: 'receiveDailyReward',
    getDailyLuckyList: 'getDailyLuckyList',
    getStarlightRank: 'getStarlightRank',
    getOfficialAnchorList: 'getOfficialAnchorList',
    receiveOfficialReward: 'receiveOfficialReward',
};

/** @type {function(import('./api.d.ts').initReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').initRes},any]>} */
export const init = (data, config) => fetchApi({ api: REQUEST_API_MAP.init, data, config });

/** @type {function(import('./api.d.ts').inviteReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').common.emptyRes},any]>} */
export const invite = (data, config) => fetchApi({ api: REQUEST_API_MAP.invite, data, config });

/** @type {function(import('./api.d.ts').acceptInviteReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').common.emptyRes},any]>} */
export const acceptInvite = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.acceptInvite, data, config });

/** @type {function(import('./api.d.ts').getInviteListReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getInviteListRes},any]>} */
export const getInviteList = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getInviteList, data, config });

/** @type {function(import('./api.d.ts').getAcceptListReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getInviteListRes},any]>} */
export const getAcceptList = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getAcceptList, data, config });

/** @type {function(import('./api.d.ts').getTeamStarlightRecordReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getTeamStarlightRecordRes},any]>} */
export const getTeamStarlightRecord = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getTeamStarlightRecord, data, config });

/** @type {function(import('./api.d.ts').initReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getMyStarlightRes},any]>} */
export const getMyStarlight = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getMyStarlight, data, config });

/** @type {function(import('./api.d.ts').initReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').common.emptyRes},any]>} */
export const receiveDailyReward = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.receiveDailyReward, data, config });

/** @type {function(import('./api.d.ts').initReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getDailyLuckyList},any]>} */
export const getDailyLuckyList = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getDailyLuckyList, data, config });

/** @type {function(import('./api.d.ts').getStarlightRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getStarlightRankRes},any]>} */
export const getStarlightRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getStarlightRank, data, config });

/** @type {function(import('./api.d.ts').initReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getOfficialAnchorListRes},any]>} */
export const getOfficialAnchorList = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getOfficialAnchorList, data, config });

/** @type {function(import('./api.d.ts').initReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').common.emptyRes},any]>} */
export const receiveOfficialReward = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.receiveOfficialReward, data, config });

export default {
    init,
    invite,
    acceptInvite,
    getInviteList,
    getAcceptList,
    getTeamStarlightRecord,
    getMyStarlight,
    receiveDailyReward,
    getDailyLuckyList,
    getStarlightRank,
    getOfficialAnchorList,
    receiveOfficialReward,
};
