import { defineStore } from 'pinia';
import { ref } from 'vue';
// import useInitStore from './use-init-store';
import { getCurrentChannelInfo } from '@/utils/jsbridge';
import { env } from '@/config/url';
/**
 * 直播间信息
 */
const useChannelInfo = defineStore('channelInfo', () => {
    // const initStore = useInitStore();
    const info = ref({});

    // const anchorInfo = computed(() => initStore?.initData?.anchorInfo);

    const update = () => {
        let anchorInfo = getCurrentChannelInfo();

        console.log('anchorInfo', anchorInfo);

        try {
            if (typeof anchorInfo === 'string') {
                anchorInfo = JSON.parse(anchorInfo);
                console.log('anchorInfo', anchorInfo);
            }
        }
        catch (error) {
            anchorInfo = null;
        }

        const query = parseUrlQuery();
        if (query?.channel_id && query?.creator_uid && env !== 'prod') {
            console.log('query', query);
            anchorInfo = {
                type: 7,
                creator_uid: query?.creator_uid,
                channel_id: query?.channel_id,
            };
        }
        // anchorInfo = {
        //     type: 7,
        //     creator_uid: 2404417,
        //     channel_id: 2042727,
        // };
        // anchorInfo = {
        //     type: 7,
        //     creator_uid: 2405343,
        //     channel_id: 2041769,
        // };
        // if (anchorInfo?.type) {
        //     info.value = anchorInfo;
        // }
        if (anchorInfo?.type === 7) {
            info.value = anchorInfo;
        }

        console.table('房间信息：', anchorInfo);
    };

    const channelId = computed(() => info.value?.channel_id);
    const anchorUid = computed(() => info.value?.creator_uid);

    const isInRoom = computed(() => !!toValue(channelId));

    update(); // 先更新一下

    return {
        info,
        channelId,
        anchorUid,
        update,
        isInRoom,
        // anchorInfo,
    };
});

export default useChannelInfo;
