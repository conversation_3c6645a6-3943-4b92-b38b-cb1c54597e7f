import { defineStore } from 'pinia';
import API from '@/api';
import useChannelInfo from '@/stores/modules/use-channel-info';

/**
 * @typedef {object} Init
 * @property {import('../../api/api.d.ts').InitResp} initData 初始化数据
 * @property {number} serverTime 服务器时间戳（秒）
 */

const useInitStore = defineStore('init', () => {
    /** @type {Init} */
    const stateInit = {
        initData: {},
        followInitData: {},
        serverTime: 0,
    };
    // 业务数据
    const stateApi = {
        // ...
    };
    const state = reactive(Object.assign({}, stateInit, stateApi));

    const isEnd = computed(() => {
        const { endTime } = state.initData;
        return state.serverTime >= endTime;
    });

    let countTimeKey = null;
    // 模拟服务器时间
    function countServerTime(serverTime) {
        if (countTimeKey) {
            clearInterval(countTimeKey);
            countTimeKey = null;
        }
        window.serverTime = serverTime;
        state.serverTime = serverTime;
        countTimeKey = setInterval(() => {
            window.serverTime += 1;
            state.serverTime += 1;
        }, 1000);
    }

    /**
     * @name init 数据初始化
     * @type {function(import('../../api/api.d.ts').InitReq)}
     */
    const init = async (payload = {}, config = {}) => {
        const { anchorUid } = storeToRefs(useChannelInfo());
        console.log('anchorUid', anchorUid);
        const [{ code, data }] = await API.init({
            anchorUid: anchorUid.value,
        }, config);
        if (code === 0) {
            state.initData = data || {};
            state.followInitData = data;
            if (data?.anchorInfo) {
                state.followInitData = {
                    ...data?.anchorInfo,
                };
            }
            console.log('state.followInitData', state.followInitData);
            countServerTime(data.serverTime);
        }
        return { code };
    };

    return {
        ...toRefs(state),
        init,
        isEnd,
    };
});

export default useInitStore;
