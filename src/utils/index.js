import Tween from '@tt/tween';
import dayjs from 'dayjs';
import html2canvas from 'html2canvas';
import Graphemer from 'graphemer';
import {
    ALL_HOST,
    JSBRIDGE_HOST,
    SHARE_HOST,
    avatarUrl,
    env,
    jsBridgePreview,
    webHost,
    webHostShare,
} from '@/config/url';
import { rewardMap } from '@/config/reward';
import { getVersion } from '@/utils/jsbridge';

const splitter = new Graphemer();

window.tween = new Tween();
/**
 * stopDoubleClick: 防止重复触发
 * getUrlParams: 提取URL上的query
 * formatTime: 时间格式化
 * getAvatar: 获取头像地址
 * omitValue: 科学计数转换
 * countdown: 倒计时
 * sleep: 休眠
 * requestAnimationFrameFn: 轮播
 * findIndex: 获取下标
 * isIphoneX: 判断是否iphoneX
 * isInENV: 根据当前URL判断当前环境是否在指定环境列表中
 * loadScriptSync: 加载JS脚本
 * filterParams: 过滤参数中指定的key
 * params2search: 参数对象转UrlSearch
 * scrollToAnchor: 滚动到锚点
 * downloadImage: 下载图片
 * num2percent: 数字转百分比
 * supplementDouble: 补齐数值两位
 * getByteLen: 判断字符串的字节长度
 * sliceByte: 按字节切割字符串
 */

/**
 * @name stopDoubleClick 防止重复触发按钮
 * @param {Vue} vm vue组件的实例
 * @param {number} delay 按钮禁用的延迟时间
 * @return {boolean} 是否处于点击中
 * @example if (!stopDoubleClick(this, 500)) return;
 */
export const stopDoubleClick = (vm, delay) => {
    if (vm.isVmClick) return false;

    vm.isVmClick = true;
    setTimeout(() => {
        vm.isVmClick = false;
    }, delay || 1000);
    return true;
};

/**
 * @name formatTime 转换为时间格式方法自定义方式
 * @param {number} value 时间戳
 * @param {string} format 格式比如"yyyy-MM-dd hh:mm:ss"
 * @param {string} unit 时间戳单位 ['s' | 'ms']
 * @returns {string} 格式化后的时间
 */
export const formatTime = (value = Date.now() / 1000, format = 'yyyy-MM-dd', unit = 's') => {
    let distance = new Date().getTimezoneOffset();
    distance = distance + 8 * 60;
    if (unit === 's') value *= 1000;
    value += distance * 60 * 1000;
    const time = new Date(value);
    let targetFormat = format || 'yyyy-MM-dd h:m:s';
    const date = {
        'Y+': time.getFullYear(),
        'M+': time.getMonth() + 1,
        'd+': time.getDate(),
        'h+': time.getHours(),
        'm+': time.getMinutes(),
        's+': time.getSeconds(),
        'q+': Math.floor((time.getMonth() + 3) / 3),
        'S+': time.getMilliseconds(),
    };
    const re = /(y+)/i;
    if (re.test(targetFormat)) {
        const t = re.exec(targetFormat)[1];
        targetFormat = targetFormat.replace(t, `${time.getFullYear()}`.substring(4 - t.length));
    }
    for (const k in date) {
        const regx = new RegExp(`(${k})`);
        if (regx.test(targetFormat)) {
            const t = regx.exec(targetFormat)[1];
            targetFormat = targetFormat.replace(
                t,
                t.length === 1 ? date[k] : `00${date[k]}`.substring(`${date[k]}`.length),
            );
        }
    }
    return targetFormat;
};

/**
 * @name formatTxt 省略文本
 * @param {string} txt 文本
 * @param {number} length 显示长度
 * @returns {string} 省略后的文本
 */
export const omitTxt = (txt, length = 5) => {
    return txt !== undefined && txt.length > length ? `${txt.substring(0, length)}...` : txt;
};

/**
 * @name getAvatar 获取头像
 * @param {number | string} account ttid/房间id/公会id
 * @param {number} type 头像类型 1:用户头像 2:房间头像 3:公会头像
 * @returns {string} 头像地址
 */
export const getAvatar = (account, type = 1) => {
    const extra = type === 2 ? '@channel' : type === 3 ? '@guild' : '';
    // 灰度跟正式需要加上size=small后缀
    return `${avatarUrl}${account || 0}${extra}${
        ['dev', 'testing'].includes(env) ? '' : '?size=small'
    }`;
};

/**
 * @name omitValue 省略数值
 * @param {number} val 数值
 * @param {number} threshold 阈值 默认10000
 * @param {string} txt 省略单位 默认'w'
 */
export const omitValue = (val, threshold = 10000, txt = 'w') => {
    return val >= threshold ? Math.floor(val / 100) / 100 + txt : val;
};

/**
 * @name countdown
 * @param {number} endTime 秒数
 * @returns [天,时,分,秒] 字符串数组
 */
export const countdown = (endTime) => {
    const time = endTime;
    let day = Number.parseInt(time / (60 * 60 * 24));
    let hour = Number.parseInt((time % (60 * 60 * 24)) / (60 * 60));
    let minute = Number.parseInt((time % (60 * 60)) / 60);
    let second = Number.parseInt(time % 60);
    day = day >= 10 ? `${day}` : `0${day}`;
    hour = hour >= 10 ? `${hour}` : `0${hour}`;
    minute = minute >= 10 ? `${minute}` : `0${minute}`;
    second = second >= 10 ? `${second}` : `0${second}`;
    return [day, hour, minute, second];
};

/**
 * @name sleep 休眠
 * @param {number} ms 休眠时间（毫秒）
 * @returns {Promise} 返回
 */
export const sleep = async (ms) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(true);
        }, ms);
    });
};

/**
 * @name requestAnimationFrame 左右无缝轮播
 * 兼容requestAnimationFrame
 */
export const requestAnimationFrameFn = () => {
    let lastTime = 0;
    const nextFrame =
        window.requestAnimationFrame ||
        window.mozRequestAnimationFrame ||
        window.msRequestAnimationFrame ||
        function (callback) {
            const currTime = +new Date();
            const delay = Math.max(1000 / 60, 1000 / 60 - (currTime - lastTime));
            lastTime = currTime + delay;
            return setTimeout(callback, delay);
        };
    const cancelFrame =
        window.cancelAnimationFrame ||
        window.webkitCancelRequestAnimationFrame ||
        window.mozCancelRequestAnimationFrame ||
        window.msCancelRequestAnimationFrame ||
        clearTimeout;
    return [nextFrame, cancelFrame];
};

export const findIndex = (array, predicate, context) => {
    for (let i = 0; i < array.length; i++) {
        if (predicate.call(context, array[i], i, array)) return i;
    }

    return -1;
};

/**
 * @name isIphoneX 判断是否是iphoneX
 * @returns {boolean} 是否是iphoneX
 */
export const isIphoneX = () => {
    const iphone = navigator.userAgent.includes('iphone');
    const isIphoneX =
        window.devicePixelRatio &&
        window.devicePixelRatio === 3 &&
        window.screen.width === 375 &&
        iphone;
    return isIphoneX;
};

/**
 * @name loadScriptSync 加载JS脚本
 * @param {string | Array} src 脚本地址
 * @returns {Promise} 返回一个Promis.all
 */
export const loadScriptSync = (src) => {
    let params = src;
    if (typeof src === 'string') params = [src];
    const scripts = [];
    for (let i = 0; i < params.length; i++) {
        scripts[i] = new Promise((resolve, reject) => {
            const HEAD = document.getElementsByTagName('head')[0] || document.documentElement;
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.onload = () => {
                resolve(script);
            };
            script.onerror = () => {
                reject(false);
            };
            script.src = params[i];
            HEAD.appendChild(script);
        });
    }
    return Promise.all(scripts);
};

/**
 * @name loadRemoteFile 加载远程文件,json
 * @param {string} url 远程资源地址
 * @returns {promise} 返回一个promise
 */
export const loadRemoteFile = (url) => {
    return new Promise((resolve) => {
        const rawFile = new XMLHttpRequest();
        rawFile.overrideMimeType('application/json');
        rawFile.open('GET', url, true);
        rawFile.onreadystatechange = function () {
            if (rawFile.readyState === 4 && +rawFile.status === 200) resolve(rawFile.responseText);
        };
        rawFile.send(null);
    });
};

/**
 * @name filterParams 过滤参数中指定的key
 * @param {object} params 参数对象
 * @param {Array} list 需过滤的key列表
 */
export const filterParams = (params, list = []) => {
    if (Object.keys(params).length < 1) return {};
    const res = Object.assign({}, params);
    Object.keys(res).forEach((key) => {
        if (list.includes(key)) delete res[key];
    });
    return res;
};

/**
 * @name params2search 参数对象转UrlSearch
 * @param {object} params 参数对象
 */
export const params2search = (params) => {
    if (!params || Object.keys(params).length < 1) return '';
    return `?${Object.keys(params)
        .reduce(
            (accumulator, currentValue) => `${accumulator}&${currentValue}=${params[currentValue]}`,
            '',
        )
        .slice(1)}`;
};

/**
 * @name scrollToAnchor 滚动到锚点
 * @param {string} contentSelector 滚动容器的选择器
 * @param {string} domSelector 锚点DOM的选择器ID
 * @param {number} distance 偏移距离
 * @param {string} direction 滚动方向
 */
export const scrollToAnchor = (contentSelector, domSelector, distance = 0, direction = 'y') => {
    const view =
        typeof contentSelector === 'string'
            ? document.querySelector(contentSelector)
            : contentSelector;
    const dom = typeof domSelector === 'string' ? document.querySelector(domSelector) : domSelector;
    if (!view || !dom) return;
    const scrollField = direction === 'x' ? 'scrollLeft' : 'scrollTop';
    const scrollFieldClient = direction === 'x' ? 'left' : 'top';
    const scrollFieldLength = direction === 'x' ? 'scrollWidth' : 'scrollHeight';
    const clientFieldLength = direction === 'x' ? 'clientWidth' : 'clientHeight';
    const start = view[scrollField];
    const order =
        start +
        dom.getBoundingClientRect()[scrollFieldClient] -
        view.getBoundingClientRect()[scrollFieldClient] -
        (document.documentElement.clientWidth / 375) * distance;
    const maxOrder = contentSelector[scrollFieldLength] - contentSelector[clientFieldLength];

    window.tween.animation(
        start,
        order > maxOrder ? maxOrder : order,
        (value) => {
            view[scrollField] = value;
        },
        500,
        Tween.EASING_TYPE.QUAD_EASEINOUT,
    );
};

/**
 * @name downloadImage 下载图片
 * @param {string} path 图片地址
 */
export const downloadImage = (path) => {
    return new Promise((resolve, reject) => {
        const image = new Image();
        image.onload = resolve;
        image.onerror = reject;
        image.src = path;
    });
};

/**
 * @name num2percent 数字转百分比
 * @param {number} num
 */
export const num2percent = (num) => {
    return num === 0 ? '0%' : `${Math.floor(num * 100)}%`;
};

/**
 * @name supplementDouble 补齐数值两位
 * @param {number | string} num
 */
export const supplementDouble = (num) => {
    const n = Number.parseInt(num, 10);
    return n > 0 ? (n <= 9 ? `0${n}` : n) : '00';
};

// 判断字符串字节长度
export const getByteLen = (val) => {
    let len = 0;
    for (let i = 0; i < val.length; i++) {
        const a = val.charAt(i);

        if (a.match(/[^\x00-\xFF]/gi) != null) len += 2;
        else len += 1;
    }
    return len;
};

/**
 * @name sliceByte 按字节切割字符串
 * @param {string} str 要切割的字符串
 * @param {number} byteLength 切割长度
 */
export const sliceByte = (str, byteLength) => {
    let result = '';
    let length = 0;
    const strArr = str.split('');

    for (let i = 0; i < strArr.length; i++) {
        const char = str[i];
        const charCode = char.charCodeAt(0);

        // 判断是否为 emoji
        // [\uDC00-\uDFFF]
        if (/[\uD800-\uDBFF]/.test(char)) {
            // 处理 emoji
            if (length + 2 > byteLength) break;
            result += char + str[i + 1];
            length += 2;
            i++; // 跳过下一个字符，因为 emoji 是两个字符
        } else if (charCode <= 0x007f) {
            // ASCII 字符
            if (length + 1 > byteLength) break;
            result += char;
            length += 1;
        } else {
            // 非 ASCII 字符（包括中文）
            if (length + 2 > byteLength) break;
            result += char;
            length += 2;
        }
    }

    return result;
};

/**
 * @name momentToDayBegin 把传入的dayjs对象设为当天0点
 * @param {*} m
 */
export const dayjsToDayBegin = (m) => {
    return m.clone().hour(0).minute(0).second(0).millisecond(0);
};

/**
 * @name momentToDayBegin 把传入的dayjs对象设为当天23:59:59:999点
 * @param {*} m
 */
export const dayjsToDayEnd = (m) => {
    return m.clone().hour(23).minute(59).second(59).millisecond(999);
};

// 子元素滚动到父元素中间
export function scrollIntoViewMiddle(childElem, parentElem) {
    const parentRect = parentElem.getBoundingClientRect();
    const childRect = childElem.getBoundingClientRect();
    const parentWidth = parentElem.clientWidth;
    const parentHeight = parentElem.clientHeight;
    const childWidth = childElem.offsetWidth;
    const childHeight = childElem.offsetHeight;
    const parentTop = parentRect.top;
    const parentLeft = parentRect.left;
    const childTop = childRect.top;
    const childLeft = childRect.left;
    const scrollYDistance = childTop - parentTop + childHeight / 2 - parentHeight / 2;
    const scrollXDistance = childLeft - parentLeft + childWidth / 2 - parentWidth / 2;
    parentElem.scrollTo({
        left: parentElem.scrollLeft + scrollXDistance,
        top: parentElem.scrollTop + scrollYDistance,
        behavior: 'smooth',
    });
}

/**
 * @name formatNumber 自定义省略数值
 * @param {number | string} num
 * @param {number} n 幂次方
 * @param {number} demical 保留小数位数
 * @param {number} txt 数字后缀
 */
export const formatNumber = (num = 0, n = 4, demical = 2, txt = 'W') => {
    const numIn = +num;
    const threshold = 10 ** n;
    // 叠加原有小数位数
    const numStr = numIn.toString();
    const numDemicalIndex = numStr.indexOf('.');
    const demicalOrigin = numDemicalIndex === -1 ? 0 : numStr.slice(numDemicalIndex + 1).length;
    return numIn >= threshold
        ? Number(
              (numIn / threshold)
                  .toFixed(demical + (n + demicalOrigin))
                  .slice(0, -(n + demicalOrigin)),
          ) + txt
        : num;
};

/**
 * @name getRandom 获取区间随机数
 * @param {*} min
 * @param {*} max
 * @returns 随机数
 */
export const getRandom = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * @name throttle 节流
 * @param {Function} fn 函数
 * @param {int} delay 触发时间间隔
 */
export const throttle = (fn, delay) => {
    let startTime;
    let timer;
    return function (...args) {
        const now = Date.now();
        if (!startTime) startTime = now;
        if (now - startTime >= delay) {
            startTime = now;
            fn.apply(this, args);
            clearTimeout(timer);
        } else if (timer) {
            return;
        }
        timer = setTimeout(() => {
            startTime = Date.now();
            timer = null;
            fn.apply(this, args);
        }, delay);
    };
};

export const getRewardInfo = (id) => {
    const info = rewardMap[id] ? { ...rewardMap[id] } : {};
    let imageUrl = '';
    try {
        imageUrl = requireImg(`rewards/${info.image}`);
    } catch {
        const canvas = document.createElement('canvas');
        // 绘制文字环境
        const context = canvas.getContext('2d');
        // 画布宽度
        canvas.width = 100;
        // 画布高度
        canvas.height = 100;

        // 设置水平对齐方式
        context.textAlign = 'center';
        // 设置垂直对齐方式
        context.textBaseline = 'middle';

        // 填充白色
        context.fillStyle = '#ffffff';
        // 绘制文字之前填充白色
        context.fillRect(0, 0, canvas.width, canvas.height);

        context.fillStyle = '#000000';
        context.font = '30px Arial';
        // 绘制文字（参数：要写的字，x坐标，y坐标）
        context.fillText(id, canvas.width / 2, canvas.height / 2);
        imageUrl = canvas.toDataURL('image/png');
    }
    return imageUrl;
};

export const getRewardDataInfo = (id) => {
    const query = parseUrlQuery();
    const info = rewardMap[id] ? { ...rewardMap[id] } : {};
    let imageUrl = '';
    try {
        imageUrl = requireImg(`rewards/${info.image}`);
        if (query?.rawReward) {
            throw new Error('展示原始奖励，用 id 展示');
        }
    } catch {
        const canvas = document.createElement('canvas');
        // 绘制文字环境
        const context = canvas.getContext('2d');
        // 画布宽度
        canvas.width = 100;
        // 画布高度
        canvas.height = 100;

        // 设置水平对齐方式
        context.textAlign = 'center';
        // 设置垂直对齐方式
        context.textBaseline = 'middle';

        // 填充白色
        context.fillStyle = '#ffffff';
        // 绘制文字之前填充白色
        context.fillRect(0, 0, canvas.width, canvas.height);

        context.fillStyle = '#000000';
        context.font = '30px Arial';
        // 绘制文字（参数：要写的字，x坐标，y坐标）
        context.fillText(id, canvas.width / 2, canvas.height / 2);
        imageUrl = canvas.toDataURL('image/png');
    }
    return { ...info, imageUrl };
};

/**
 * 复制单行内容到粘贴板
 * content : 需要复制的内容
 * fn : 复制完回调
 */
export const copyToClip = (content, fn = () => {}) => {
    const textarea = document.createElement('textarea'); // 直接构建textarea  「注意：这里为了实现换行，需要创建textarea，如果用input的话，实现不了换行。」
    textarea.readOnly = true;
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    textarea.value = content; // 设置内容    「注意： \r\n 是 换行 符号」
    document.body.appendChild(textarea); // 添加临时实例
    textarea.select(); // 选择实例内容
    const result = document.execCommand('Copy');
    if (result)
        // eslint-disable-next-line no-console
        console.log('复制成功，复制内容为：', content);

    document.body.removeChild(textarea); // 删除临时实例
    if (typeof fn === 'function') fn && fn();

    return content;
};

export const numberToChinese = (num) => {
    const units = '个十百千万@#%亿^&~';
    const chars = '零一二三四五六七八九';
    const a = `${num}`.split('');
    const s = [];
    const j = a.length - 1;
    if (a.length > 12) throw new Error('数字太大，无法解析');

    for (let i = 0; i <= j; i++) {
        if (j === 1 || j === 5 || j === 9) {
            // 两位数 处理特殊的 1*
            if (i === 0) {
                if (a[i] !== '1') s.push(chars.charAt(+a[i]));
            } else {
                s.push(chars.charAt(+a[i]));
            }
        } else {
            s.push(chars.charAt(+a[i]));
        }
        if (i !== j) s.push(units.charAt(j - i));
    }
    // return s;
    return (
        s
            .join('')
            .replace(/零([十百千万亿@#%^&~])/g, (m, d, b) => {
                // 优先处理 零百 零千 等
                b = units.indexOf(d);
                if (b !== -1) {
                    if (d === '亿') return d;
                    if (d === '万') return d;
                    if (a[j - b] === '0') return '零';
                }
                return '';
            })
            .replace(/零+/g, '零')
            .replace(/零([万亿])/g, (m, b) => {
                // 处理 零万 零亿
                return b;
            })
            .replace(/亿[万千百]/g, '亿')
            .replace(/零$/, '')
            .replace(/^零/, '')
            // .replace(/十$/, '十零')
            .replace(/^一十/, '十')
            .replace(/@[^@]*@/g, (m) => {
                return m.replace(/@/g, '');
            })
    );
};

/**
 * @name getDomBase64 获取元素截屏base64
 * @param {HTMLElement} el 截屏元素
 * @param {object} params html2canvas参数
 * @returns {string} base64
 */
export const getDomBase64 = async (el, params = {}) => {
    if (!el) {
        showToast('生成图片失败');
        console.error('请传入目标元素');
        return;
    }
    if (myWebview.isInApp() && myWebview.isIOS() && getVersion() < 362) {
        showToast('当前客户端版本不支持该操作，请升级最新版本');
        return;
    }
    const height = el.offsetHeight;
    const { timeout = 15000, scale = 2, backgroundColor = null } = params;
    const options = {
        useCORS: true,
        height,
        imageTimeout: timeout,
        backgroundColor,
        scale,
        ignoreElements(e) {
            /**
             * 解决iOS设备在"0ms Starting document clone"后卡住的问题
             * fix 方案：https://github.com/niklasvh/html2canvas/issues/3053#issuecomment-1674068819
             */
            if (
                (e.tagName === 'A' && e.host !== window.location.host) ||
                e.getAttribute('loading') === 'lazy'
            ) {
                return true;
            }
            if (typeof e.className === 'string' && e.className.match('ignore')) {
                return true;
            }
            return false;
        },
    };
    /**
     * line height 在 html2canvas 方案有点问题
     * fix 方案：https://github.com/niklasvh/html2canvas/issues/2775#issuecomment-1316356991
     */
    const style = document.createElement('style');
    document.head.appendChild(style);
    style.sheet?.insertRule('body > div:last-child img { display: inline-block; }');
    try {
        const canvas = await html2canvas(el, options);
        const cropImage = canvas.toDataURL('image/jpeg', 0.8);
        return cropImage;
    } catch (error) {
        showToast('生成图片失败');
        console.error('生成图片base64失败', error);
    } finally {
        style.remove();
    }
};

/**
 * @description app版本字符型数字型互转，自动判断
 * @name convertAppVersion
 * @param version {string|number}
 * @param force {string} 强制为某种类型 string | number
 * @returns {string|number} 转换后的版本
 */
export const convertAppVersion = (version, force) => {
    if (
        (force === 'string' && typeof version === 'string') ||
        (force === 'number' && !Number.isNaN(Number(version)))
    ) {
        return force === 'number' ? Number(version) : version;
    }
    // 根据客户端兼容性，转换相应版本 (算法同客户端)
    if (`${version}`.includes('.')) {
        // 字符版本转数字版本
        const split = version.split('.');
        let result = 0;
        result |= split[0] << 24;
        result |= (split[1] || 0) << 16;
        result |= (split[2] || 0) << 0;
        return result;
    }
    // 数字版本转字符版本
    let str = Number.parseInt(version, 10).toString(2);
    const arrVer = ['', '', ''];
    arrVer[2] = Number.parseInt(str.substring(str.length - 16), 2).toString(10);
    str = str.substring(0, str.length - 16);
    arrVer[1] = Number.parseInt(str.substring(str.length - 8), 2).toString(10);
    str = str.substring(0, str.length - 8);
    arrVer[0] = Number.parseInt(str, 2).toString(10);
    return arrVer.join('.');
};

/**
 * @name afterVersion 当前app是否大于等于指定版本
 * @description // afterVersion('6.54.0') || afterVersion(104071169)
 * @param version {string|number}
 */
export const afterVersion = (version) => {
    const currentVersion = getVersion();
    const curVersion = convertAppVersion(currentVersion, 'number');
    const targetVersion = convertAppVersion(version, 'number');
    return curVersion >= targetVersion;
};

/**
 * @name convertMarketUrl  转换成对应马甲包的链接
 * @param {string} url 原链接
 * @returns {string} 转换后的链接
 */
export const convertMarketUrl = (url) => {
    // 检测是否为活动域名
    const originHost = ALL_HOST.find((host) => url.startsWith(host));
    if (originHost) {
        const isShare = SHARE_HOST.some((host) => url.startsWith(host));
        return isShare ? url.replace(originHost, webHostShare) : url.replace(originHost, webHost);
    }
    // 检测是否为app短链
    const originJsbridgeHost = JSBRIDGE_HOST.find((host) => url.startsWith(host));
    if (originJsbridgeHost) return url.replace(originJsbridgeHost, jsBridgePreview);
    return url;
};

export const safeOmitTxt = (txt, length = 7) => {
    if (!txt || txt.length <= length) return txt;
    const codeArr = splitter.splitGraphemes(txt);
    return `${codeArr.slice(0, length).join('')}...`;
};
/**
 * @name getIncludesDays 传入begin、end，根据日历获取相距天数(包含当天)
 */
export const getIncludesDays = (begin, end) => {
    return dayjsToDayBegin(dayjs.unix(end)).tz().diff(dayjsToDayBegin(dayjs.unix(begin).tz()), 'day') + 1;
};
/**
 * @name createExternalClickHandler  封装可复用的点击外部处理器
 * @param {string} options
 */
export const createExternalClickHandler = (options) => {
    const {
        target, // 需要监听外部的目标元素
        excludeSelectors = [], // 需要排除的选择器数组
        callback, // 触发回调
    } = options;

    // 获取所有排除元素
    const excludeElements = excludeSelectors.map(s => document.querySelector(s));

    // 点击处理函数
    const handleClick = (e) => {
        // 检查是否在目标元素内部
        const isInsideTarget = target.contains(e.target);

        // 检查是否在排除元素内部
        const isInExcluded = excludeElements.some((el) => {
            return el && el.contains(e.target);
        });
        // 执行条件判断
        if (!isInsideTarget && !isInExcluded) {
            callback(e);
        }
    };
    // 返回控制器
    return {
        start: () => document.addEventListener('click', handleClick),
        stop: () => document.removeEventListener('click', handleClick),
    };
};