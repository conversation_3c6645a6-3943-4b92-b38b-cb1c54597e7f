import axios from 'axios';
import { getToken } from './jsbridge';
import { env, nodeUrl } from '@/config/url';
import config from '@/config';

export const createAxios = (axiosConfig = {}) => {
    const request = axios.create({
        baseURL: nodeUrl(),
        timeout: 10 * 1000, // 请求超时时间
        ...axiosConfig,
    });

    const httpErrorToast = (error) => {
        const { toast = true } = error?.config || {};
        if (config.__isOffline.startsWith('____ActivityOfflineTag____') || !toast)
            return;

        const requestUrl = error?.config?.url || {};
        let text = '';
        if (error.response)
            text
                = env === 'prod'
                    ? `服务器开小差了，请稍后重试~`
                    : `服务器开小差了，请稍后重试~[${requestUrl} ]`;
        else if (error.request)
            text
                = env === 'prod'
                    ? `请求超时了，请稍后重试~`
                    : `请求超时了，请稍后重试~ [${requestUrl} - timeout - ${Date.now()}]`;
        else
            text
                = env === 'prod'
                    ? `${error.message}`
                    : `${error.message} [${requestUrl} - ${Date.now()}]`;
        showToast({ message: text });
    };

    // 请求拦截器
    request.interceptors.request.use(
        (conf) => {
            // 添加token
            const token = getToken() || undefined;
            if (conf.method === 'get')
                conf.params = Object.assign(
                    { token, uid: myWebview.params.uid || undefined },
                    conf.params,
                );
            else if (conf.method === 'post')
                conf.data = Object.assign(
                    { token, uid: myWebview.params.uid || undefined },
                    conf.data,
                );

            if (token)
                conf.headers.Authorization = token;

            if (env === 'gray' || myWebview.params?.requestGray === 1)
                // 灰度环境 或者 参数强制，添加灰度标识
                conf.headers['x-qw-traffic-mark'] = 'staging';
            return conf;
        },
        (error) => {
            httpErrorToast(error);
            return Promise.reject(error);
        },
    );

    // 异常拦截处理器
    request.interceptors.response.use(
        (response) => {
            const data = response.data;
            const { toast = true } = response.config;

            if (
                data.code !== 0
                && toast
                && config.__isOffline.startsWith('____ActivityOnlineTag____')
            )
                showToast({
                    message: !data.msg
                        ? '服务器开小差了，请稍后重试~'
                        // : `${data.msg} (${data.code})`,
                        : `${data.msg}`,
                });

            return data;
        },
        (error) => {
            httpErrorToast(error);
            return Promise.reject(error);
        },
    );

    return request;
};


const drawHttp = createAxios({
    baseURL: nodeUrl({nodePath:'starlight-encounter-2508'}),
    baseNodeUrl: nodeUrl({nodePath:'starlight-encounter-2508'}),
});

export { drawHttp };

export default createAxios();
