export const rewardList = [
    {
        resource_id: 'A1',
        name: '鎏金星光',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 5000,
        special_type: '礼物架豆豆礼物',
        image: 'A1.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/136e-1989c3e7b82.png',
    },
    {
        resource_id: 'A2',
        name: '幻紫星光',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 10,
        special_type: '',
        image: 'A2.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/5731-1989c46b728.png',
    },
    {
        resource_id: 'A3',
        name: '高级',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'S级',
        image: 'A3.png',
    },
    {
        resource_id: 'A4',
        name: '中级',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'A级',
        image: 'A4.png',
    },
    {
        resource_id: 'A5',
        name: '璀璨星河',
        type: 'other',
        mark: '房间背景',
        unit: '天',
        special_type: '',
        image: 'A5.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/yunying/8d8e-1989881eb05.png',
    },
    {
        resource_id: 'A6',
        name: '幸福记录',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A6.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/acbc-18ee1541fa8.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/9b80-18ee1543607.png',
    },
    {
        resource_id: 'A7',
        name: '鹊桥赴约',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 50000,
        special_type: '豆豆礼物',
        image: 'A7.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/b439-17b33391b62.png',
    },
    {
        resource_id: 'A8',
        name: '梦，遇',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A8.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/4729-17aa3b454a0.png',
    },
    {
        resource_id: 'A9',
        name: '繁星召唤',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A9.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/63b4-1803ca3fe60.png',
    },
    {
        resource_id: 'A10',
        name: '童趣少女',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A10.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/1d06-19464a3323b.png',
    },
    {
        resource_id: 'A11',
        name: '星光大道冠军导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A11.png',
    },
    {
        resource_id: 'A12',
        name: '星光大道三强导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A12.png',
    },
    {
        resource_id: 'A13',
        name: '星光大道五强导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A13.png',
    },
    {
        resource_id: 'A14',
        name: '星光大道十强导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A14.png',
    },
    {
        resource_id: 'A15',
        name: '星光大道优质导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A15.png',
    },
    {
        resource_id: 'A16',
        name: '星辉天使',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'A16.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/95eb-198988c16f0-fd7103780c99272a69be897dd14d20c2.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/581c-19889191a1a.zip',
    },
    {
        resource_id: 'A17',
        name: '普通',
        type: 'flow_card',
        mark: '流量卡',
        unit: '张',
        special_type: '普通流量卡',
        image: 'A17.png',
    },
    {
        resource_id: 'A18',
        name: '云顶之翼',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A18.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/c768-18aaca03e71.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/5140-18aaca05097.png',
    },
    {
        resource_id: 'A19',
        name: '梦幻星球',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'A19.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/7c29-198987e30d4.webp',
    },
    {
        resource_id: 'A20',
        name: '初级',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'B级',
        image: 'A20.png',
    },
    {
        resource_id: 'A21',
        name: '星光幸运导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A21.png',
    },
    {
        resource_id: 'A22',
        name: '星光幸运学员',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A22.png',
    },
    {
        resource_id: 'A23',
        name: '怦然心动达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A23.png',
    },
    {
        resource_id: 'A24',
        name: '星月相伴',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'A24.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/a120-198988a3f49-785cb523a60b67f7b9becb43e90022e0.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/d95d-1988918af3d.zip',
    },
    {
        resource_id: 'A25',
        name: '星动鉴赏官',
        type: 'user_plate',
        mark: '个人铭牌',
        unit: '天',
        special_type: '',
        image: 'A25.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/4bcb-1987d5becd7.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/channel/nameplate/ic_approve_xdjsg250805',
    },
    {
        resource_id: 'A26',
        name: '星权法杖',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 1000,
        special_type: '红钻礼物',
        image: 'A26.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/cfb3-1989c3c2bb8.png',
    },
    {
        resource_id: 'A27',
        name: '幻紫星光',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10,
        special_type: '红钻礼物',
        image: 'A27.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/5731-1989c46b728.png',
    },
];

export const rewardMap = {
    A1: {
        resource_id: 'A1',
        name: '鎏金星光',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 5000,
        special_type: '礼物架豆豆礼物',
        image: 'A1.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/136e-1989c3e7b82.png',
    },
    A2: {
        resource_id: 'A2',
        name: '幻紫星光',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 10,
        special_type: '',
        image: 'A2.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/5731-1989c46b728.png',
    },
    A3: {
        resource_id: 'A3',
        name: '高级',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'S级',
        image: 'A3.png',
    },
    A4: {
        resource_id: 'A4',
        name: '中级',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'A级',
        image: 'A4.png',
    },
    A5: {
        resource_id: 'A5',
        name: '璀璨星河',
        type: 'other',
        mark: '房间背景',
        unit: '天',
        special_type: '',
        image: 'A5.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/yunying/8d8e-1989881eb05.png',
    },
    A6: {
        resource_id: 'A6',
        name: '幸福记录',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A6.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/acbc-18ee1541fa8.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/9b80-18ee1543607.png',
    },
    A7: {
        resource_id: 'A7',
        name: '鹊桥赴约',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 50000,
        special_type: '豆豆礼物',
        image: 'A7.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/b439-17b33391b62.png',
    },
    A8: {
        resource_id: 'A8',
        name: '梦，遇',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A8.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/4729-17aa3b454a0.png',
    },
    A9: {
        resource_id: 'A9',
        name: '繁星召唤',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A9.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/63b4-1803ca3fe60.png',
    },
    A10: {
        resource_id: 'A10',
        name: '童趣少女',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A10.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/1d06-19464a3323b.png',
    },
    A11: {
        resource_id: 'A11',
        name: '星光大道冠军导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A11.png',
    },
    A12: {
        resource_id: 'A12',
        name: '星光大道三强导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A12.png',
    },
    A13: {
        resource_id: 'A13',
        name: '星光大道五强导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A13.png',
    },
    A14: {
        resource_id: 'A14',
        name: '星光大道十强导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A14.png',
    },
    A15: {
        resource_id: 'A15',
        name: '星光大道优质导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A15.png',
    },
    A16: {
        resource_id: 'A16',
        name: '星辉天使',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'A16.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/95eb-198988c16f0-fd7103780c99272a69be897dd14d20c2.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/581c-19889191a1a.zip',
    },
    A17: {
        resource_id: 'A17',
        name: '普通',
        type: 'flow_card',
        mark: '流量卡',
        unit: '张',
        special_type: '普通流量卡',
        image: 'A17.png',
    },
    A18: {
        resource_id: 'A18',
        name: '云顶之翼',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A18.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/c768-18aaca03e71.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/5140-18aaca05097.png',
    },
    A19: {
        resource_id: 'A19',
        name: '梦幻星球',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'A19.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/7c29-198987e30d4.webp',
    },
    A20: {
        resource_id: 'A20',
        name: '初级',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'B级',
        image: 'A20.png',
    },
    A21: {
        resource_id: 'A21',
        name: '星光幸运导师',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A21.png',
    },
    A22: {
        resource_id: 'A22',
        name: '星光幸运学员',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A22.png',
    },
    A23: {
        resource_id: 'A23',
        name: '怦然心动达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A23.png',
    },
    A24: {
        resource_id: 'A24',
        name: '星月相伴',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'A24.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/a120-198988a3f49-785cb523a60b67f7b9becb43e90022e0.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/d95d-1988918af3d.zip',
    },
    A25: {
        resource_id: 'A25',
        name: '星动鉴赏官',
        type: 'user_plate',
        mark: '个人铭牌',
        unit: '天',
        special_type: '',
        image: 'A25.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/4bcb-1987d5becd7.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/channel/nameplate/ic_approve_xdjsg250805',
    },
    A26: {
        resource_id: 'A26',
        name: '星权法杖',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 1000,
        special_type: '红钻礼物',
        image: 'A26.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/cfb3-1989c3c2bb8.png',
    },
    A27: {
        resource_id: 'A27',
        name: '幻紫星光',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10,
        special_type: '红钻礼物',
        image: 'A27.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/5731-1989c46b728.png',
    },
};
