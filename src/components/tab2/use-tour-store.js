// src/stores/modules/use-tour-store.js
import { defineStore } from 'pinia';
import { getOfficialAnchorList, receiveOfficialReward } from '@/api';
import useFollow from '@/use/use-follow';
import useInitStore from '@/stores/modules/use-init-store';

/**
 * 官频巡演状态管理
 */
const useTourStore = defineStore('tour', () => {
    const { followUser } = useFollow();
    const { serverTime } = storeToRefs(useInitStore());

    const state = reactive({
        // 列表
        list: [],
        // 加载状态
        loading: false,
        // 礼物状态
        rewardStatus: 0,
    });

    // 模拟当前场次数据（实际应该从API获取）
    const currentSchedule = computed(() => ({
        anchors: state.list || [],
    }));

    // 模拟当前主播ID（实际应该从API获取）
    const currentAnchorId = computed(() => {
        return state.list.find(anchor => anchor.startTime <= serverTime.value && anchor.endTime >= serverTime.value,
        )?.userInfo?.uid;
    });

    // 是否有主播
    const hasAnchors = computed(() => state.list && state.list.length > 0);

    // 领取
    async function handleReserve() {
        try {
            const [{ code }] = await receiveOfficialReward();
            if (code === 0) {
                showToast('领取成功');
                state.rewardStatus = 2;
            }
        }
        catch (error) {
            console.error('领取失败:', error);
            showToast('网络错误，请稍后重试');
        }
    }
    /**
     * 获取巡演列表
     */
    async function fetchTourList() {
        try {
            state.loading = true;
            state.error = null;

            const [{ code, data }] = await getOfficialAnchorList();

            if (code === 0) {
                Object.assign(state, data);
            }
            else {
                state.error = '获取巡演数据失败';
            }
        }
        catch (error) {
            console.error('获取巡演列表失败:', error);
            state.error = '网络错误，请稍后重试';
        }
        finally {
            state.loading = false;
        }
    }

    /**
     * 关注主播
     */
    async function handleFollowAnchor(anchorId) {
        try {
            // 更新本地状态
            followUser(anchorId).then((res) => {
                if (res) {
                    const anchor = state.list?.find(a => a?.userInfo?.uid === anchorId);
                    if (anchor) {
                        anchor.isFollow = true;
                    }
                    showToast('关注成功');
                }
            });
        }
        catch (error) {
            console.error('关注主播失败:', error);
            showToast('关注失败，请稍后重试');
        }
    }
    /**
     * 跳转到主播直播间
     */
    function goToAnchorRoom(roomId) {
        if (!roomId) {
            showToast('直播间信息错误');
            return;
        }

        // 使用项目中的跳转方法
        toRoom(roomId);
    }

    /**
     * 重置状态
     */
    function reset() {
        state.list = [];
        state.loading = false;
        state.error = null;
    }

    return {
        ...toRefs(state),
        // computed properties
        currentSchedule,
        currentAnchorId,
        hasAnchors,
        // methods
        fetchTourList,
        handleFollowAnchor,
        goToAnchorRoom,
        reset,
        handleReserve,
    };
});

export default useTourStore;
