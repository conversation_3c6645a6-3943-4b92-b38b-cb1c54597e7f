<!-- src/components/tab3/audio-progress-bar.vue -->
<template>
    <div class="audio-progress-bar">
        <!-- 进度条容器 -->
        <div
            ref="progressContainer"
            class="progress-container"
            @click="handleProgressClick"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="handleTouchEnd"
        >
            <!-- 进度条背景 -->
            <div class="progress-track">
                <!-- 已播放进度 -->
                <div
                    class="progress-fill"
                    :style="{ width: `${progress}%` }"
                ></div>
                <!-- 进度条滑块 -->
                <div
                    class="progress-thumb"
                    :style="{ left: `${progress}%` }"
                ></div>
            </div>
        </div>
    </div>
</template>

<script setup>
// src/components/tab3/audio-progress-bar.vue

const props = defineProps({
    // 当前播放时间（秒）
    currentTime: {
        type: Number,
        default: 0,
    },
    // 总时长（秒）
    duration: {
        type: Number,
        default: 0,
    },
    // 播放进度百分比 (0-100)
    progress: {
        type: Number,
        default: 0,
    },
    // 格式化的当前时间
    formattedCurrentTime: {
        type: String,
        default: '00:00',
    },
    // 格式化的总时长
    formattedDuration: {
        type: String,
        default: '00:00',
    },
});

const emit = defineEmits(['progressChange']);

const progressContainer = ref(null);
const isDragging = ref(false);

// 处理进度条点击
function handleProgressClick(event) {
    if (!props.duration)
        return;

    const rect = progressContainer.value.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const progressPercent = (clickX / rect.width) * 100;

    // 限制在 0-100 范围内
    const clampedProgress = Math.max(0, Math.min(100, progressPercent));

    emit('progressChange', clampedProgress);
}

// 处理触摸开始
function handleTouchStart(event) {
    if (!props.duration)
        return;

    isDragging.value = true;
    event.preventDefault();
}

// 处理触摸移动
function handleTouchMove(event) {
    if (!isDragging.value || !props.duration)
        return;

    event.preventDefault();

    const touch = event.touches[0];
    const rect = progressContainer.value.getBoundingClientRect();
    const touchX = touch.clientX - rect.left;
    const progressPercent = (touchX / rect.width) * 100;

    // 限制在 0-100 范围内
    const clampedProgress = Math.max(0, Math.min(100, progressPercent));

    emit('progressChange', clampedProgress);
}

// 处理触摸结束
function handleTouchEnd() {
    isDragging.value = false;
}

// 监听鼠标事件（桌面端支持）
onMounted(() => {
    const handleMouseMove = (event) => {
        if (!isDragging.value || !props.duration)
            return;

        const rect = progressContainer.value.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const progressPercent = (mouseX / rect.width) * 100;

        // 限制在 0-100 范围内
        const clampedProgress = Math.max(0, Math.min(100, progressPercent));

        emit('progressChange', clampedProgress);
    };

    const handleMouseUp = () => {
        isDragging.value = false;
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    onBeforeUnmount(() => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    });
});
</script>

<style lang="less" scoped>
.audio-progress-bar {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.progress-container {
    position: relative;
    width: 100%;
    height: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px 0;
}

.progress-track {
    position: relative;
    width: 100%;
    height: 100%;
    background: #191527;
    border-radius: 7.5px;
}

.progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(0deg, #f7ce8c 0%, #e3a0b5 100%), #3e3797;
    border-radius: 7.5px;
    transition: width 0.1s ease;
}

.progress-thumb {
    position: absolute;
    top: 50%;
    width: 31px;
    height: 31px;
    background-image: url('@/assets/img/tab3/<EMAIL>');
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: left 0.1s ease;
    background-size: 100% 100%;
    &:hover {
        transform: translate(-50%, -50%) scale(1.2);
    }
}
</style>
