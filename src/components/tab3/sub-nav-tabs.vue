<template>
    <div class="sub-nav-tabs">
        <div
            v-for="item of navConfig"
            :key="item.value"
            class="nav-tab"
            @click="handleNavChange(item.value)"
        >
            <div
                v-if="props.modelValue === item.value "
                class="bg-default h-[29px] w-[85px] flex justify-center pt-5 text-13 text-[#F8DE8F]"
                :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"

            >
                {{ item.label }}
            </div>
            <div
                v-else
                class="pt-5 text-[11px] text-[#BBA6A1]">
                {{ item.label }}
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    modelValue: {
        type: Number,
        default: 1,
    },
    navConfig: {
        type: Array,
        default: () => [],
    },
});

const emit = defineEmits(['update:modelValue', 'change']);

const handleNavChange = (value) => {
    if (props.modelValue === value)
        return;

    emit('change', value);
};
</script>

<style lang="less" scoped>
.sub-nav-tabs {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 170px;

    .nav-tab {
        display: flex;
        justify-content: center;
        position: relative;
        width: 85px;
        height: 29px;
        cursor: pointer;

        &:last-child {
            margin-right: 0;
        }

        .nav-tab-image {
            height: 100%;
            width: 100%;
            object-fit: contain;
        }
    }
}
</style>
