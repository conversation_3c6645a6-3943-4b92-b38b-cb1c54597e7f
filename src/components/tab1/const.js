export const recordArr = [
    {
        task: '每累计开播1h',
        value: 200,
        desc: '',
        unit: 'min',
    },
    {
        task: '每新增一个粉团（活动期间去重）',
        value: 100,
        desc: '每日限完成20次',
        unit: '个',
    },
    {
        task: '每新增一个付费人数（每日去重）',
        value: 50,
        desc: '每日限完成20次',
        unit: '个',
    },
    {
        task: '导师和学员之间每完成一场PK连麦',
        value: 50,
        desc: '每日限完成10次',
        unit: '次',
    },
    {
        task: '导师每累计在学员直播间上麦10min',
        value: 100,
        desc: '每日限完成6次',
        unit: '次',
    },
    {
        task: '每收到1个鎏金星光50豆礼物',
        value: 5,
        desc: '',
        unit: '个',
        other: '【鎏金星光】可在礼物架送出',
    },
    {
        task: '每收到1个幻紫星光礼物',
        value: 5,
        desc: '每日限完成100次',
        unit: '个',
        other: '【幻紫星光】每日触机发放至+w名听听活跃用户背包',
    },
];
export const levelMap = {
    6: 'S级学员',
    5: 'A级学员',
    4: 'B级学员',
    3: 'C级学员',
    2: 'D级学员',
    1: 'E级学员',
    0: '学员',
};
