<!-- eslint-disable vue/no-parsing-error -->
<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="team-up-popup">
            <div class="top">
                <img
                    class="avatar"
                    :src="getAvatar(initData?.userInfo?.username)" />我的身份：{{ initData?.identity === 1 ? '导师' : levelMap[initData?.level] }}<img
                        class="tips-icon"
                        :src="requireImg('tab1/<EMAIL>')"
                        @click="showTipsContent = !showTipsContent" />
                <div
                    v-show="showTipsContent"
                    ref="tipsContent"
                    class="tipsContent">
                    <p>
                        <span>导师定义：</span>满足【首次签约>6个月】&【近3个月均活跃天>15天】&【近3个月均总收礼>1w元】&【当前粉团>500】的达人
                    </p>
                    <p>
                        <span>学员人数：</span>满足【首次签约<3个月】&【精英及以下】&【考核B级及以上】的达人
                    </p>
                </div>
            </div>
            <div class="topbar">
                <div class="tab">
                    <p
                        :class="{ active: tabIndex === 0 }"
                        @click="changeTab(0)">
                        我发出邀请
                    </p>
                    <p
                        :class="{ active: tabIndex === 1 }"
                        @click="changeTab(1)">
                        我收到邀请
                    </p>
                </div>
                <div class="search">
                    <input
                        v-model="searchText"
                        type="text"
                        placeholder="输入TTID搜索">
                    <div
                        class="search-btn"
                        @click="toSearch"></div>
                </div>
            </div>
            <div
                ref="scrollBox"
                class="user-box">
                <div
                    v-for="(item, index) in resultList"
                    class="user">
                    <div class="user-pic">
                        <!-- <img
                            v-if="item?.identity === 1"
                            class="label ds"
                            :src="requireImg(`tab1/<EMAIL>`)" />
                        <template v-else-if="item?.identity === 2">
                            <img
                                v-if="item?.level === 6"
                                class="label xy"
                                :src="requireImg(`tab1/<EMAIL>`)" />
                            <img
                                v-else-if="item?.level === 5"
                                class="label xy"
                                :src="requireImg(`tab1/<EMAIL>`)" />
                            <img
                                v-else-if="item?.level === 4"
                                class="label xy"
                                :src="requireImg(`tab1/<EMAIL>`)" />
                            <img
                                v-else
                                class="label ds"
                                :src="requireImg(`tab1/<EMAIL>`)" />
                        </template> -->
                        <identity-level-icon
                            :identity="item?.identity"
                            :level="item?.level" />
                        <img
                            class="avatar"
                            :src="getAvatar(item?.userInfo?.username)"
                            alt=""
                            @click="toPerson(item?.userInfo?.username)">
                    </div>
                    <div class="user-name">{{ item?.userInfo?.nickname }}</div>
                    <!-- 发出邀请 -->
                    <template v-if="tabIndex === 0">
                        <img
                            v-if="item?.acceptStatus === 0"
                            class="btn"
                            :src="requireImg('tab1/<EMAIL>')"
                            @click="toInvite(item?.userInfo?.uid, index)" />
                        <img
                            v-else-if="item?.acceptStatus === 1"
                            class="btn"
                            :src="requireImg('tab1/<EMAIL>')" />
                    </template>
                    <template v-else>
                        <img
                            class="btn"
                            :src="requireImg('tab1/<EMAIL>')"
                            @click="useEventBus('confirm-team-dialog').emit({ show: true, inviterNickname: item?.userInfo?.nickname, inviterUid: item?.userInfo?.uid })" />
                    </template>
                </div>
                <div
                    v-if="resultList.length"
                    class="load-more-text">
                    {{ resultList.length < teamTotal ? '上拉加载更多' : '已加载完全部数据' }}
                </div>
                <div
                    v-if="!resultList.length"
                    class="no-data">
                    暂无数据<br />
                    <template v-if="searchText">搜索的达人无活动参与资格或已成功组队</template>
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import useTab1Store from './hooks/use-tab1-store';
import { levelMap } from './const';
import { createExternalClickHandler } from '@/utils/index';
import useInitStore from '@/stores/modules/use-init-store';

const scrollBox = ref();
const { serverTime, initData } = storeToRefs(useInitStore());
const { getInviteListApi, getAcceptListApi, inviteApi, updateInviteList, resetListData } = useTab1Store();
const { acceptList, inviteList, teamTotal } = storeToRefs(useTab1Store());
const isShow = ref(false);
const tabIndex = ref(0);
const showTipsContent = ref(false);
const tipsContent = ref();
const searchText = ref('');
let page = 1;
let size = 12;

const resetParams = () => {
    page = 1;
    resetListData();
};
const requestData = () => {
    if (tabIndex.value === 0) {
        getInviteListApi({
            page,
            size,
            ...(searchText.value
                ? {
                        ttid: searchText.value,
                    }
                : {}),
        });
    }
    else {
        getAcceptListApi({
            page,
            size,
            ...(searchText.value
                ? {
                        ttid: searchText.value,
                    }
                : {}),
        });
    }
};
const changeTab = (index) => {
    if (tabIndex.value === index)
        return;
    if (searchText.value) {
        searchText.value = '';
    }
    tabIndex.value = index;
    resetParams();
    requestData();
};
const resultList = computed(() => {
    return tabIndex.value === 0 ? inviteList.value : acceptList.value;
});
const toInvite = async (inviteeUid, index) => {
    const { code } = await inviteApi({
        inviteeUid,
    });
    if (code === 0) {
        showToast('已成功发出邀请');
        updateInviteList(index);
    }
};
const toSearch = () => {
    resetParams();
    requestData();
};
let handler = null;
let loadMoreLock = false;

const loadMore = () => {
    page += 1;
    requestData();
};
// onMounted(() => {

// });
const initListData = () => {
    tabIndex.value = 0;
    if (searchText.value) {
        searchText.value = '';
    }
    resetParams();
    requestData();
};
useEventBus('team-up-popup').on(async ({ show = true }) => {
    isShow.value = show;
    initListData();
    await nextTick();
    // 点击元素以外区域触发
    handler = createExternalClickHandler({
        target: tipsContent.value,
        excludeSelectors: ['.tipsContent', '.tips-icon'], // 支持多个排除元素
        callback: () => {
            // console.log('触发外部点击');
            if (showTipsContent.value) {
                showTipsContent.value = false;
            }
        // handler.stop(); // 可选：关闭后停止监听
        },
    });
    // 初始化显示并启动监听
    handler.start();
    scrollBox.value.onscroll = () => {
        const el = scrollBox.value;
        console.log('滚动到底部了');
        // 滚动到底部了
        if (el.scrollHeight - el.clientHeight <= el.scrollTop) {
            if (resultList.value.length >= teamTotal.value) {
                return;
            }
            // 节流处理 在限定时间只内执行一次
            if (loadMoreLock) {
                return;
            }
            loadMoreLock = true;
            setTimeout(() => {
                console.log('加载');
                loadMore();
                // this.$emit('loadMore');
                loadMoreLock = false;
            }, 500);
        }
    };
});
onMounted(() => {
    setTimeout(() => {
        // console.log('ss', tipsContent.value);
        // // 点击元素以外区域触发
        // const handler = createExternalClickHandler({
        //     target: document.querySelector('.tips-content'),
        //     excludeSelectors: [''], // 支持多个排除元素
        //     callback: () => {
        //         showTipsContent.value = false;
        //         // console.log('触发外部点击');
        //     // handler.stop(); // 可选：关闭后停止监听
        //     },
        // });

        // // 初始化显示并启动监听
        // // document.getElementById('target').style.display = 'block';
        // handler.start();
    }, 1000);
});
</script>

<style lang="less" scoped>
.team-up-popup {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 428px);
    padding-top: 55px;
    .top {
        width: 100%;
        display: flex;
        justify-content: center;
        font-size: 16px;
        font-weight: normal;
        text-align: left;
        color: #ffe29c;
        height: 28px;
        align-items: center;
        margin-bottom: 30px;
        position: relative;
        .avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .tips-icon {
            width: 13px;
            height: 13px;
            margin-left: 4px;
            margin-top: -10px;
        }
        .tipsContent {
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 291px, 148px);
            padding-top: 24px;
            padding-left: 22px;
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 3;
            p {
                width: 250px;
                font-size: 12px;
                font-weight: normal;
                text-align: left;
                color: #5a5a5a;
                line-height: 18px;
                margin-bottom: 16px;
                span {
                    color: #df8c2a;
                }
            }
        }
    }
    .topbar {
        width: 100%;
        display: flex;
        justify-content: center;
        .tab {
            display: flex;
            justify-content: center;
            font-size: 13px;
            font-weight: normal;
            text-align: center;
            color: #bba6a1;
            height: 24px;
            line-height: 24px;
            margin-right: 20px;
            p {
                width: 80px;
            }
            .active {
                color: #fdff74;
                font-size: 14px;
            }
        }
        .search {
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 124px, 24px);
            padding-left: 8px;
            position: relative;
            input {
                background: none;
                width: 90px;
                height: 24px;
                border: none;
                font-size: 12px;
                color: #fff;
            }
            .search-btn {
                width: 28px;
                height: 24px;
                position: absolute;
                right: 0;
                top: 0;
                z-index: 2;
            }
        }
    }
    .user-box {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        flex-flow: wrap;
        padding-top: 20px;
        height: 280px;
        overflow-x: hidden;
        overflow-y: scroll;
        padding-left: 8px;
        margin-top: 10px;
        .user {
            width: 70px;
            margin: 0 10px 14px;
            &-pic {
                width: 50px;
                height: 50px;
                // .full-bg('@/assets/img/tab1/<EMAIL>');
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0 auto 3px;
                position: relative;
                .avatar {
                    width: 46px;
                    height: 46px;
                    border-radius: 50%;
                    border: 2px solid #d2d3d3;
                }
            }
            &-name {
                font-size: 11px;
                font-weight: 400;
                text-align: center;
                color: #ffebc7;
                width: 100%;
                .one-line();
                margin-bottom: 8px;
            }
            .btn {
                width: 64px;
                height: 28px;
                margin: 0 auto;
            }
        }
    }
    .no-data {
        width: 100%;
        font-size: 14px;
        font-weight: normal;
        text-align: center;
        color: #d2cee5;
        line-height: 24px;
        text-align: center;
        margin-top: 80px;
    }
    .load-more-text {
        width: 100%;
        font-size: 9px;
        font-weight: normal;
        text-align: center;
        color: #d2cee5;
        line-height: 26px;
        text-align: center;
    }
}
</style>
