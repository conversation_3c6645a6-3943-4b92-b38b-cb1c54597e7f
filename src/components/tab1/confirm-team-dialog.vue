<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <div class="confirm-team-dialog">
            <p class="t1">确认接受主播<span>{{ toNnickname }}</span>的组队邀请吗? </p>
            <p class="t2">组队成功后将不可更改!</p>
            <div class="input-box">
                <textarea
                    v-model="confirmText"
                    type="text"
                    placeholder="请手动输入“接受邀请”并点击确认完成组队操作"></textarea>
                <p @click="fastWrite">一键写入</p>
            </div>
            <div class="btn-box">
                <img
                    :src="requireImg('tab1/<EMAIL>')"
                    alt=""
                    @click="isShow = false">
                <img
                    :src="requireImg('tab1/<EMAIL>')"
                    alt=""
                    @click="toAcceptInvite">
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import useTab1Store from './hooks/use-tab1-store';
import useInitStore from '@/stores/modules/use-init-store';

const initStore = useInitStore();
const { acceptInviteApi } = useTab1Store();
const isShow = ref(false);
const confirmText = ref('');
const fastWrite = () => {
    confirmText.value = '';
    confirmText.value = '接受邀请';
};
let toNnickname = ref('');
let toUid = ref(ref(0));
const toAcceptInvite = async () => {
    if (confirmText.value !== '接受邀请') {
        showToast('确认指令错误，请重试');
        return;
    }
    const { code } = await acceptInviteApi({
        inviterUid: toUid.value,
    });
    if (code === 0) {
        showToast('恭喜你们组队成功');
        initStore.init();
        isShow.value = false;
        useEventBus('team-up-popup').emit({ show: false });
        setTimeout(() => {
            location.reload();
        }, 100);
    }
};
useEventBus('confirm-team-dialog').on(({ show = true, inviterNickname = '', inviterUid = '' }) => {
    isShow.value = show;
    toNnickname.value = inviterNickname;
    toUid.value = inviterUid;
});
</script>

<style lang="less" scoped>
.confirm-team-dialog {
    width: 375px;
    height: 220px;
    padding-top: 52px;
    .full-bg('@/assets/img/tab1/<EMAIL>');
    .t1 {
        font-size: 13px;
        font-weight: 500;
        text-align: center;
        color: #332e46;
        line-height: 17.5px;
        display: flex;
        justify-content: center;
        span {
            display: inline-block;
            width: 80px;
            .one-line();
        }
    }
    .t2 {
        font-size: 13px;
        font-weight: 500;
        text-align: center;
        color: #e07134;
        line-height: 17.5px;
        margin-bottom: 24px;
    }
    .input-box {
        width: 200px;
        margin: 0 auto;
        textarea {
            border: none;
            color: #333;
            font-size: 13px;
            width: 200px;
            height: 30px;
            margin-bottom: 6px;
        }
        textarea::placeholder {
            opacity: 0.5;
            color: #606060;
            font-size: 12px;
        }
        p {
            font-size: 13px;
            font-weight: 500;
            text-align: right;
            color: #b83516;
        }
    }
    .btn-box {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 70px;
        img {
            width: 128px;
            height: 38px;
            margin: 0 5px;
        }
    }
}
</style>
