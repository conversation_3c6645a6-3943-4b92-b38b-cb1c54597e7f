<template>
    <div>
        <div class="record-box">
            <p>·达人每日完成活动任务可获得星光值</p>
            <p>·学员考核S/A级的队伍可得20%/10%加成</p>
            <img
                v-if="followInitData?.identity"
                class="btn"
                :src="requireImg('tab1/<EMAIL>')"
                alt=""
                @click="useEventBus('my-record-popup').emit({ show: true })">
        </div>
        <Total-day-select
            class="total-day-select-box"
            @change-date="changeDate" />
        <div
            v-if="currentTab !== 'total'"
            class="area-box">
            <div
                v-for="(item, index) in areaArr"
                :class="{ active: areaIndex === index }"
                @click="changeArea(index)">
                {{ item }}区
            </div>
        </div>
        <div
            v-if="initData?.identity && initData?.userInfo?.uid === followInitData?.userInfo?.uid && !initData?.bindInfo"
            class="team-entry"
            @click="useEventBus('team-up-popup').emit({ show: true })">
            <identity-level-icon
                :identity="initData?.identity"
                :level="initData?.level" />
            <img
                class="avatar"
                :src="getAvatar(initData?.userInfo?.username)"
                alt="">
            <img
                class="icon"
                :src="requireImg('tab1/<EMAIL>')"
                alt=""
            >
        </div>
        <div
            class="reward-box"
            :class="isTotal ? 'total_bg' : 'day_bg'">
            <p>需成功组队后才可上榜,接活动期间队伍累计星光值排名</p>
            <div class="rewards">
                <Tab1-reward-item
                    v-for="(item, index) in rewardsArr"
                    :item="item" />
            </div>
        </div>
        <div
            class="rank-box"
            :class="{ 'pb-[160px]': initData?.identity, 'pt-[244px]': isTotal && rankList.length }">
            <tab1-rank-item
                v-for="(item, index) in rankList"
                :is-today="isToday"
                :is-total="isTotal"
                :column-top3-style="isTotal"
                :item="item" />
            <div
                v-if="!rankList.length"
                class="no-data">
                暂无数据
            </div>
        </div>
        <!-- <unteamed-rank-me v-if="initData?.identity && initData?.userInfo?.uid === followInitData?.userInfo?.uid && !initData?.bindInfo" /> -->
        <tab1-rank-me v-if="followInitData?.identity && Object.keys(self).length" />

        <my-record-popup />
        <team-up-popup />
        <confirm-team-dialog />
    </div>
</template>

<script setup>
import useRankStore from './hooks/use-rank-store';
import useDateSelectStore from './hooks/use-date-select-store';
import useInitStore from '@/stores/modules/use-init-store';

const rankStore = useRankStore();
const dateSelectStore = useDateSelectStore();
const { isToday, isTotal } = storeToRefs(useDateSelectStore());
const { currentTab } = storeToRefs(useDateSelectStore());
const { serverTime, initData, followInitData } = storeToRefs(useInitStore());
const { rankList, self } = storeToRefs(useRankStore());
const areaArr = ['A', 'B', 'C', 'D', 'E'];
const areaIndex = ref(-1);
const focusNowArea = () => {
    if (!followInitData?.value?.dailyTeamRaceId) {
        areaIndex.value = 0;
    }
    else {
        areaIndex.value = areaArr.indexOf(followInitData?.value?.dailyTeamRaceId);
    }
    console.log('focusNowArea');
};
const changeDate = () => {
    console.log('changeDate:', currentTab.value);
    focusNowArea();
    setTimeout(() => {
        rankStore.initRankList({ ...(currentTab.value === 'total' ? {} : { dailyTeamRaceId: areaArr[areaIndex.value] }) });
    }, 100);
};
const rewardsArr = [
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
];
const changeArea = (index) => {
    if (areaIndex.value === index)
        return;
    areaIndex.value = index;
    rankStore.initRankList({ dailyTeamRaceId: areaArr[areaIndex.value] });
};
useEventBus('scroll-to-bottom').on(() => {
    console.log('下一页');
    rankStore.loadMore({ ...(currentTab.value === 'total' ? {} : { dailyTeamRaceId: areaArr[areaIndex.value] }) });
});
onMounted(() => {
    focusNowArea();
});
</script>

<style lang="less" scoped>
.record-box {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 350px, 49px);
    margin: 0 auto;
    position: relative;
    padding-left: 63px;
    font-size: 12px;
    font-weight: normal;
    color: #ffebc7;
    display: flex;
    justify-content: center;
    flex-direction: column;
    p {
        line-height: 20px;
    }
    .btn {
        width: 77px;
        height: 20px;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 2;
    }
}
.reward-box {
    width: 375px;
    height: 119px;
    padding-left: 50px;
    padding-top: 8px;
    p {
        color: #ffebc7;
        font-size: 11px;
    }
    .rewards {
        width: 310px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        overflow-x: scroll;
        overflow-y: hidden;
        height: 100px;
    }
}
.day_bg {
    .full-bg('@/assets/img/tab1/<EMAIL>');
}
.total_bg {
    .full-bg('@/assets/img/tab1/<EMAIL>');
}
.area-box {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 26px);
    display: flex;
    justify-content: center;
    align-items: center;
    div {
        width: 60px;
        text-align: center;
        color: #bba6a1;
        font-size: 12px;
    }
    div.active {
        color: #fdff74;
    }
}
.rank-box {
    padding-bottom: 80px;
    margin-top: 18px;
    position: relative;
}
.no-data {
    width: 100%;
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    color: #d2cee5;
    line-height: 24px;
    text-align: center;
    margin-top: 80px;
}
.team-entry {
    position: fixed;
    bottom: 180px;
    right: 8px;
    z-index: 3;
    width: 54px;
    height: 54px;
    /deep/ .label {
        left: auto;
        transform: none;
        right: 0;
    }
    .avatar {
        width: 51px;
        height: 51px;
        border-radius: 50%;
        margin: 0 auto;
        border: 2px solid #ffe68f;
    }
    .icon {
        width: 53px;
        height: 18px;
        position: absolute;
        left: 0;
        bottom: -4px;
        z-index: 2;
    }
}
</style>
