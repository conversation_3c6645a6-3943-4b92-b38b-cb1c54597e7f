<template>
    <img
        v-if="identity === 1"
        class="label ds"
        :src="requireImg(`tab1/<EMAIL>`)" />
    <template v-else-if="identity === 2">
        <img
            v-if="level === 6"
            class="label xy"
            :src="requireImg(`tab1/<EMAIL>`)" />
        <img
            v-else-if="level === 5"
            class="label xy"
            :src="requireImg(`tab1/<EMAIL>`)" />
        <img
            v-else-if="level === 4"
            class="label xy"
            :src="requireImg(`tab1/<EMAIL>`)" />
        <img
            v-else
            class="label ds"
            :src="requireImg(`tab1/<EMAIL>`)" />
    </template>
</template>

<script setup>
const props = defineProps({
    identity: {
        type: Number,
        default: 0,
    },
    level: {
        type: Number,
        default: 0,
    },
});
</script>

<style lang="less" scoped>
.label {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -5px;
    z-index: 2;
}
.ds {
    width: 25px;
    height: 11px;
}
.xy {
    width: 40px;
    height: 11px;
}
</style>
