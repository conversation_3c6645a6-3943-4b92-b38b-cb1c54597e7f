<template>
    <div
        v-if="item?.channelId && item?.status > 1 && item?.status <= 4"
        class="room-status"
        :class="`status${item?.status}`"
        alt=""
        @click="handleAvatar(item?.channelId, 2)">
    </div>
</template>

<script setup>
const props = defineProps({
    item: {
        type: Object,
        default() {
            return {};
        },
    },
});
</script>

<style lang="less" scoped>
.room-status {
    position: absolute;
    left: 50%;
    bottom: -2px;
    transform: translateX(-50%);
    width: 40px;
    height: 15px;
    background: #af4025;
    line-height: 13px;
    color: #fff;
    font-size: 9px;
    text-align: center;
    z-index: 2;
}
.status2 {
    background: url('@/assets/img/tab1/<EMAIL>');
    background-size: 100% 100%;
}
.status3 {
    background: url('@/assets/img/tab1/<EMAIL>');
    background-size: 100% 100%;
}
.status4 {
    background: url('@/assets/img/tab1/<EMAIL>');
    background-size: 100% 100%;
}
</style>
